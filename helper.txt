--- Contents of spectrogram_1 before pickling ---
Key: frequencies, Type: <class '_io.BufferedWriter'>
---------------------------------------------------
Key: times, Type: <class 'numpy.ndarray'>
---------------------------------------------------
Key: Sxx, Type: <class 'numpy.ndarray'>
---------------------------------------------------
Key: domain, Type: <class 'str'>
---------------------------------------------------
Key: primary_data, Type: <class 'str'>
---------------------------------------------------
Key: secondary_data, Type: <class 'str'>
---------------------------------------------------
Key: tertiary_data, Type: <class 'str'>
---------------------------------------------------
Key: sampling_rate, Type: <class 'int'>
---------------------------------------------------
Key: nperseg, Type: <class 'int'>
---------------------------------------------------
Key: noverlap, Type: <class 'int'>
---------------------------------------------------
Key: image_path, Type: <class 'str'>
---------------------------------------------------
Key: new_params, Type: <class 'dict'>
---------------------------------------------------

STDERR:
Traceback (most recent call last):
  File "D:\Drive\Projekty\LLM_analyzer_context\run_state\run_20250828_000847\tmpdgpbxgek.py", line 36, in <module>
    pickle.dump(spectrogram_1, f)
TypeError: cannot pickle '_io.BufferedWriter' object