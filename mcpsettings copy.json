{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequential_thinking", "sequentialthinking"], "alwaysAllow": ["sequentialthinking"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Desktop", "D:/Drive/Dropbox/Python"], "autoApprove": ["read_file", "read_multiple_files", "write_file", "edit_file", "create_directory", "list_directory", "directory_tree", "move_file", "search_files", "get_file_info", "list_allowed_directories"]}, "context7": {"command": "npx", "disabled": false, "args": ["-y", "@upstash/context7-mcp@latest"], "autoApprove": ["resolve-library-id", "get-library-docs"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "autoApprove": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "autoApprove": ["fetch"]}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "autoApprove": ["start_codegen_session", "end_codegen_session", "get_codegen_session", "clear_codegen_session", "playwright_navigate", "playwright_screenshot", "playwright_click", "playwright_iframe_click", "playwright_iframe_fill", "playwright_fill", "playwright_select", "playwright_hover", "playwright_evaluate", "playwright_console_logs", "playwright_close", "playwright_get", "playwright_post", "playwright_put", "playwright_patch", "playwright_delete", "playwright_expect_response", "playwright_assert_response", "playwright_custom_user_agent", "playwright_get_visible_text", "playwright_get_visible_html", "playwright_go_back", "playwright_go_forward", "playwright_drag", "playwright_press_key", "playwright_save_as_pdf", "playwright_click_and_switch_tab"]}, "github.com/modelcontextprotocol/servers/tree/main/src/github": {"command": "npx", "disabled": true, "args": ["-y", "@modelcontextprotocol/server-github"], "autoApprove": ["create_issue", "get_issue", "update_issue", "close_issue", "reopen_issue", "add_comment", "get_comments", "search_repositories", "get_repository", "get_repository_content", "create_pull_request", "get_pull_request", "update_pull_request", "merge_pull_request", "get_user", "get_organization", "list_repository_issues", "list_repository_pull_requests", "list_repository_branches", "get_branch", "list_repository_commits", "get_commit", "create_fork", "delete_repository", "add_collaborator", "remove_collaborator", "list_collaborators", "create_release", "get_release", "update_release", "delete_release", "upload_release_asset", "get_release_asset", "delete_release_asset", "create_gist", "get_gist", "update_gist", "delete_gist", "star_repository", "unstar_repository", "check_repository_starred", "watch_repository", "unwatch_repository", "check_repository_watched", "get_rate_limit", "get_webhook", "create_webhook", "update_webhook", "delete_webhook", "list_webhooks", "get_workflow", "list_workflows", "create_workflow_dispatch", "get_workflow_run", "list_workflow_runs", "cancel_workflow_run", "rerun_workflow_run", "get_check_run", "list_check_runs", "create_check_run", "update_check_run", "get_check_suite", "list_check_suites", "create_check_suite", "rerequest_check_suite", "get_status", "list_statuses", "create_status", "get_license", "list_licenses", "get_code_of_conduct", "list_codes_of_conduct", "get_contributing", "get_issue_template", "list_issue_templates", "get_pull_request_template", "get_security_policy", "get_topics", "replace_topics", "get_all_topics", "get_repository_tags", "get_repository_languages", "get_repository_contributors", "get_repository_teams", "get_repository_projects", "get_repository_hooks", "create_repository_hook", "update_repository_hook", "delete_repository_hook", "ping_repository_hook", "test_repository_hook", "get_repository_pages", "enable_repository_pages", "disable_repository_pages", "get_repository_pages_build", "list_repository_pages_builds", "get_repository_pages_health_check", "get_repository_traffic_views", "get_repository_traffic_clones", "get_repository_traffic_paths", "get_repository_traffic_referrers", "get_repository_code_frequency", "get_repository_commit_activity", "get_repository_participation", "get_repository_punch_card", "get_repository_stargazers", "get_repository_subscribers", "get_repository_forks", "get_repository_events", "get_repository_issue_events", "get_repository_project_cards", "get_repository_project_columns", "get_repository_projects_list", "create_repository_project", "update_repository_project", "delete_repository_project", "get_repository_project_card", "update_repository_project_card", "delete_repository_project_card", "move_repository_project_card", "get_repository_project_column", "update_repository_project_column", "delete_repository_project_column", "move_repository_project_column", "list_repository_project_cards", "list_repository_project_columns", "get_repository_milestone", "list_repository_milestones", "create_repository_milestone", "update_repository_milestone", "delete_repository_milestone", "get_repository_label", "list_repository_labels", "create_repository_label", "update_repository_label", "delete_repository_label", "get_repository_assignee", "list_repository_assignees", "check_repository_assignee", "get_repository_issue_timeline", "get_repository_issue_events_for_issue", "get_repository_issue_labels", "add_repository_issue_labels", "remove_repository_issue_label", "replace_repository_issue_labels", "delete_repository_issue_labels", "get_repository_issue_lock", "lock_repository_issue", "unlock_repository_issue", "get_repository_issue_reaction", "list_repository_issue_reactions", "create_repository_issue_reaction", "delete_repository_issue_reaction", "get_repository_pull_request_review", "list_repository_pull_request_reviews", "create_repository_pull_request_review", "update_repository_pull_request_review", "delete_repository_pull_request_review", "submit_repository_pull_request_review", "dismiss_repository_pull_request_review", "get_repository_pull_request_review_comment", "list_repository_pull_request_review_comments", "create_repository_pull_request_review_comment", "update_repository_pull_request_review_comment", "delete_repository_pull_request_review_comment", "get_repository_pull_request_review_comments_for_review", "get_repository_pull_request_files", "get_repository_pull_request_commits", "check_repository_pull_request_merged", "get_repository_pull_request_merge", "merge_repository_pull_request_with_method", "get_repository_pull_request_diff", "get_repository_pull_request_patch", "get_repository_pull_request_comments", "create_repository_pull_request_comment", "update_repository_pull_request_comment", "delete_repository_pull_request_comment", "get_repository_pull_request_comment_reaction", "list_repository_pull_request_comment_reactions", "create_repository_pull_request_comment_reaction", "delete_repository_pull_request_comment_reaction", "get_repository_pull_request_requested_reviewer", "list_repository_pull_request_requested_reviewers", "request_repository_pull_request_reviewers", "remove_repository_pull_request_reviewers", "get_repository_pull_request_labels", "add_repository_pull_request_labels", "remove_repository_pull_request_label", "replace_repository_pull_request_labels", "delete_repository_pull_request_labels", "get_repository_pull_request_milestone", "update_repository_pull_request_milestone", "remove_repository_pull_request_milestone", "get_repository_pull_request_assignee", "add_repository_pull_request_assignees", "remove_repository_pull_request_assignees", "get_repository_pull_request_branch", "get_repository_pull_request_head_branch", "get_repository_pull_request_base_branch", "get_repository_pull_request_merge_commit", "get_repository_pull_request_merge_status", "get_repository_pull_request_merge_status_for_ref", "get_repository_pull_request_merge_status_for_sha", "get_repository_pull_request_merge_status_for_pull", "get_repository_pull_request_merge_status_for_pull_request", "get_repository_pull_request_merge_status_for_pull_request_ref", "get_repository_pull_request_merge_status_for_pull_request_sha", "get_repository_pull_request_merge_status_for_pull_request_pull", "get_repository_pull_request_merge_status_for_pull_request_pull_request", "get_repository_pull_request_merge_status_for_pull_request_pull_request_ref", "get_repository_pull_request_merge_status_for_pull_request_pull_request_sha", "get_repository_pull_request_merge_status_for_pull_request_pull_request_pull", "get_repository_pull_request_merge_status_for_pull_request_pull_request_pull_request"]}, "github.com/antvis/mcp-server-chart": {"command": "cmd", "args": ["/c", "npx", "-y", "@antv/mcp-server-chart"], "disabled": false, "autoApprove": []}}}