{"mcpServers": {"sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:/Drive/Dropbox/Python"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}}