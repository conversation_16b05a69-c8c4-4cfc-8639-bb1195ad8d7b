Your current task is to parse the user's description and objective, along with provided context, and convert this information into a single, structured JSON object.

Adhere strictly to the following JSON schema:
```json
{{
  "data_summary": {{
    "type": "string",
    "domain": "string",
    "sampling_frequency_hz": "integer",
    "signal_length_sec": "float",
    "properties": ["string"]
  }},
  "system_context": {{
    "type": "string",
    "configuration": "string",
    "characteristic_frequencies": {{
      "bpfi_ratio": "float or null",
      "bpfo_ratio": "float or null",
      "ftf_ratio": "float or null",
      "bsf_ratio": "float or null",
      "unknown_fault_frequency_hz": "float or null"
    }}
  }},
  "analysis_objective": {{
    "primary_goal": "string",
    "target_fault_type": "string or null",
    "target_signal_feature": "string",
    "fallback_goal": "string or null",
    "user_data_description": "{user_data_description}",
    "user_analysis_objective": "{user_analysis_objective}" 
  }},
  "initial_hypotheses": ["string"]
}}```

If any of the provided variables has unrealistic values (i.e. sampling_frequency_hz = 1 Hz) try to extract information about this parameter from the USER-PROVIDED DATA DESCRIPTION. Caution: treat all extracted numeric scalar data as integers.

If any of the variables has been updated based on USER-PROVIDED DATA DESCRIPTION (i.e. missing "sampling_frequency_hz"), validate the values that may depend on it (i.e. "signal_length_sec" incorrectly calculated base on invalid, not yet updated "sampling_frequency_hz").

Here is the information you must use for the conversion:

GROUND TRUTH DATA STATISTICS: 
{ground_truth_summary}

RELEVANT CONTEXT FROM KNOWLEDGE BASE: 
{rag_context}

RELEVANT CONTEXT FROM TOOL BASE: 
{rag_context_tools}

TOOLKIT: 
{tools_list}

USER-PROVIDED DATA DESCRIPTION: 
{user_data_description}

USER-PROVIDED ANALYSIS OBJECTIVE: 
{user_analysis_objective}

Always formulate a fallback goal. Example: if the primary goal turns out to not be achievable (i.e. extract cyclic impulsive component), then fullfilling the fallback goal (i.e. determine informative frequency band for the component of interest) might still be useful and enable proposing further actions of the analysis pipeline.

Based on all the information above, generate the complete JSON object. Do not include any other text or explanation in your response.